<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import { Star, StarFilled } from "@element-plus/icons-vue"

defineOptions({
  name: "ReportStyle"
})

const route = useRoute()
const router = useRouter()

// 获取患者ID
const patientId = route.params.patientId as string

// 患者信息
const patientInfo = ref({
  name: "张三",
  gender: "男",
  age: "35",
  department: "心内科",
  examNumber: "US20240101001",
  examDate: "2024年01月01日",
  examPart: "心脏超声检查",
  clinicalDiagnosis: "胸闷待查"
})

// 报告内容
const reportContent = ref({
  findings: `心脏彩超检查所见：
1. 心脏形态：心脏大小正常，心房心室比例协调
2. 心脏功能：左室收缩功能正常，射血分数约65%
3. 心脏瓣膜：各瓣膜形态结构正常，启闭功能良好
4. 心包：心包无积液`,
  diagnosis: `超声诊断：
心脏彩超检查未见明显异常`
})

// 评分相关
const satisfactionScore = ref(0)
const diagnosticAccuracy = ref(0)
const feedback = ref("")

// 星级评分处理
const handleStarClick = (score: number, type: 'satisfaction' | 'accuracy') => {
  if (type === 'satisfaction') {
    satisfactionScore.value = score
  } else {
    diagnosticAccuracy.value = score
  }
}

// 大屏显示
const showFullScreen = () => {
  ElMessage.info("大屏显示功能")
}

// 发送报告
const sendReport = () => {
  ElMessage.success("报告发送成功")
}

// AI辅助报告
const aiAssistReport = () => {
  ElMessage.info("AI辅助报告功能")
}

// 编辑报告
const editReport = () => {
  ElMessage.info("编辑报告功能")
}

// 保存报告
const saveReport = () => {
  ElMessage.success("报告保存成功")
}

// 更改报告
const changeReport = () => {
  ElMessage.info("更改报告功能")
}

// 提交评分
const submitScore = () => {
  if (satisfactionScore.value === 0 || diagnosticAccuracy.value === 0) {
    ElMessage.warning("请完成评分后再提交")
    return
  }
  ElMessage.success("评分提交成功")
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化数据
  console.log("报告样式页面加载，患者ID:", patientId)
})
</script>

<template>
  <div class="report-style-page">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧报告单区域 -->
      <div class="report-section">
        <!-- 报告标题 -->
        <div class="report-header">
          <div class="hospital-info">
            <h2>XXX 医院超声查报告单</h2>
          </div>
        </div>

        <!-- 患者基本信息 -->
        <div class="patient-basic-info">
          <div class="info-row">
            <span class="label">姓名：</span>
            <span class="value">{{ patientInfo.name }}</span>
            <span class="label">性别：</span>
            <span class="value">{{ patientInfo.gender }}</span>
            <span class="label">年龄：</span>
            <span class="value">{{ patientInfo.age }}</span>
            <span class="label">科室：</span>
            <span class="value">{{ patientInfo.department }}</span>
          </div>
          <div class="info-row">
            <span class="label">检查号：</span>
            <span class="value">{{ patientInfo.examNumber }}</span>
            <span class="label">检查日期：</span>
            <span class="value">{{ patientInfo.examDate }}</span>
          </div>
          <div class="info-row">
            <span class="label">检查项目：</span>
            <span class="value">{{ patientInfo.examPart }}</span>
          </div>
        </div>

        <!-- 图片显示区域 -->
        <div class="images-section">
          <div class="image-container">
            <div class="image-placeholder">
              <span>图片 1</span>
            </div>
          </div>
          <div class="image-container">
            <div class="image-placeholder">
              <span>图片 2</span>
            </div>
          </div>
        </div>

        <!-- 超声检查所见 -->
        <div class="findings-section">
          <div class="section-title">超声检查所见：</div>
          <div class="content-area">
            {{ reportContent.findings }}
          </div>
        </div>

        <!-- 超声检查提示/超声诊断 -->
        <div class="diagnosis-section">
          <div class="section-title">超声检查提示/超声诊断：</div>
          <div class="content-area">
            {{ reportContent.diagnosis }}
          </div>
        </div>

        <!-- 医师签名区域 -->
        <div class="signature-section">
          <div class="signature-row">
            <span class="label">报告医师：</span>
            <span class="signature-line"></span>
            <span class="label">审核医师：</span>
            <span class="signature-line"></span>
            <span class="label">日期：</span>
            <span class="signature-line"></span>
          </div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="bottom-actions">
          <el-button type="primary" @click="editReport">编辑报告</el-button>
          <el-button type="success" @click="saveReport">保存报告</el-button>
          <el-button type="warning" @click="changeReport">更改报告</el-button>
        </div>
      </div>

      <!-- 右侧功能区域 -->
      <div class="function-section">
        <!-- 大屏显示按钮 -->
        <div class="fullscreen-section">
          <el-button type="primary" size="large" @click="showFullScreen" class="fullscreen-btn">
            大屏显示功能
          </el-button>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button type="primary" @click="sendReport" class="action-btn">
            发送报告
          </el-button>
          <el-button type="info" @click="aiAssistReport" class="action-btn">
            AI辅助报告
          </el-button>
        </div>

        <!-- 评分系统 -->
        <div class="rating-section">
          <div class="rating-item">
            <div class="rating-title">入院诊断符合分</div>
            <div class="stars">
              <el-icon
                v-for="i in 5"
                :key="`accuracy-${i}`"
                :class="['star', { active: i <= diagnosticAccuracy }]"
                @click="handleStarClick(i, 'accuracy')"
              >
                <StarFilled v-if="i <= diagnosticAccuracy" />
                <Star v-else />
              </el-icon>
            </div>
          </div>

          <div class="rating-item">
            <div class="rating-title">完成满意度</div>
            <div class="stars">
              <el-icon
                v-for="i in 5"
                :key="`satisfaction-${i}`"
                :class="['star', { active: i <= satisfactionScore }]"
                @click="handleStarClick(i, 'satisfaction')"
              >
                <StarFilled v-if="i <= satisfactionScore" />
                <Star v-else />
              </el-icon>
            </div>
          </div>

          <div class="feedback-section">
            <div class="feedback-title">评分意见</div>
            <el-input
              v-model="feedback"
              type="textarea"
              :rows="3"
              placeholder="请输入评分意见"
              class="feedback-input"
            />
          </div>

          <el-button type="primary" @click="submitScore" class="submit-score-btn">
            提交评分
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
